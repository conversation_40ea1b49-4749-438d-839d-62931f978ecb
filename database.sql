-- Restaurant Management System Database

-- Drop database if exists
DROP DATABASE IF EXISTS restaurant_management;

-- Create database
CREATE DATABASE restaurant_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE restaurant_management;

-- User roles table
CREATE TABLE roles (
    role_id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Users table (for both admin and staff)
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    full_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    role_id INT NOT NULL,
    status TINYINT(1) DEFAULT 1, -- 1: active, 0: inactive
    reset_token VARCHAR(255) DEFAULT NULL,
    reset_token_expires DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(role_id)
);

-- Food categories table
CREATE TABLE categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL,
    description TEXT,
    status TINYINT(1) DEFAULT 1, -- 1: active, 0: inactive
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Food items table
CREATE TABLE food_items (
    food_id INT PRIMARY KEY AUTO_INCREMENT,
    food_name VARCHAR(100) NOT NULL,
    category_id INT NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    description TEXT,
    image_path VARCHAR(255),
    status TINYINT(1) DEFAULT 1, -- 1: available, 0: unavailable
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(category_id)
);

-- Dining tables table
CREATE TABLE tables (
    table_id INT PRIMARY KEY AUTO_INCREMENT,
    table_number VARCHAR(20) NOT NULL UNIQUE,
    capacity INT NOT NULL,
    status ENUM('available', 'occupied', 'reserved', 'maintenance') DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Orders/Invoices table
CREATE TABLE orders (
    order_id INT PRIMARY KEY AUTO_INCREMENT,
    table_id INT NOT NULL,
    user_id INT NOT NULL, -- staff who created the order
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    total_amount DECIMAL(10, 2) DEFAULT 0,
    discount_percent DECIMAL(5, 2) DEFAULT 0,
    discount_amount DECIMAL(10, 2) DEFAULT 0,
    final_amount DECIMAL(10, 2) DEFAULT 0,
    status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES tables(table_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Order details table
CREATE TABLE order_details (
    order_detail_id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    food_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    subtotal DECIMAL(10, 2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (food_id) REFERENCES food_items(food_id)
);

-- Table transfer history
CREATE TABLE table_transfers (
    transfer_id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    from_table_id INT NOT NULL,
    to_table_id INT NOT NULL,
    transferred_by INT NOT NULL, -- user_id of staff
    transfer_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (from_table_id) REFERENCES tables(table_id),
    FOREIGN KEY (to_table_id) REFERENCES tables(table_id),
    FOREIGN KEY (transferred_by) REFERENCES users(user_id)
);

-- Payments table
CREATE TABLE payments (
    payment_id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL UNIQUE,
    payment_amount DECIMAL(10, 2) NOT NULL,
    payment_method ENUM('cash', 'credit_card', 'debit_card', 'mobile_payment') NOT NULL,
    payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    received_by INT NOT NULL, -- user_id of staff
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (received_by) REFERENCES users(user_id)
);

-- Insert initial data

-- Roles
INSERT INTO roles (role_name) VALUES 
('admin'),
('staff');

-- Default admin user (password: admin123)
-- Hash generated with: password_hash('admin123', PASSWORD_DEFAULT)
INSERT INTO users (username, password, email, full_name, role_id) VALUES
('admin', '$2y$10$YourHashWillBeGeneratedByDebugScript', '<EMAIL>', 'Admin User', 1);

-- Default staff user (password: staff123)
-- Hash generated with: password_hash('staff123', PASSWORD_DEFAULT)
INSERT INTO users (username, password, email, full_name, role_id) VALUES
('staff', '$2y$10$YourHashWillBeGeneratedByDebugScript', '<EMAIL>', 'Staff User', 2);

-- Sample food categories
INSERT INTO categories (category_name, description) VALUES
('Appetizers', 'Starters and small plates'),
('Main Course', 'Primary dishes'),
('Desserts', 'Sweet treats'),
('Beverages', 'Drinks and refreshments'),
('Specials', 'Chef\'s special dishes');

-- Sample food items
INSERT INTO food_items (food_name, category_id, price, description, image_path) VALUES
('Spring Rolls', 1, 8.99, 'Fresh vegetables wrapped in rice paper', 'images/spring_rolls.jpg'),
('Chicken Wings', 1, 12.99, 'Spicy buffalo wings with blue cheese dip', 'images/chicken_wings.jpg'),
('Grilled Salmon', 2, 22.99, 'Fresh salmon with lemon butter sauce', 'images/grilled_salmon.jpg'),
('Beef Steak', 2, 28.99, 'Juicy steak with garlic butter', 'images/beef_steak.jpg'),
('Chocolate Cake', 3, 9.99, 'Rich chocolate cake with vanilla ice cream', 'images/chocolate_cake.jpg'),
('Cheesecake', 3, 8.99, 'New York style cheesecake', 'images/cheesecake.jpg'),
('Lemonade', 4, 4.99, 'Fresh squeezed lemonade', 'images/lemonade.jpg'),
('Iced Tea', 4, 3.99, 'Refreshing iced tea with lemon', 'images/iced_tea.jpg'),
('Chef\'s Special Pasta', 5, 18.99, 'Pasta with secret sauce and ingredients', 'images/special_pasta.jpg');

-- Sample tables
INSERT INTO tables (table_number, capacity) VALUES
('A1', 2),
('A2', 2),
('B1', 4),
('B2', 4),
('C1', 6),
('C2', 6),
('D1', 8),
('VIP1', 10);

-- Create views for reporting

-- Daily revenue view
CREATE VIEW daily_revenue_view AS
SELECT 
    DATE(payment_date) AS date,
    SUM(payment_amount) AS total_revenue,
    COUNT(DISTINCT order_id) AS total_orders
FROM 
    payments
WHERE 
    payment_status = 'completed'
GROUP BY 
    DATE(payment_date);

-- Popular food items view
CREATE VIEW popular_food_items_view AS
SELECT 
    f.food_id,
    f.food_name,
    f.category_id,
    c.category_name,
    SUM(od.quantity) AS total_ordered,
    SUM(od.subtotal) AS total_revenue
FROM 
    order_details od
JOIN 
    food_items f ON od.food_id = f.food_id
JOIN 
    categories c ON f.category_id = c.category_id
JOIN 
    orders o ON od.order_id = o.order_id
WHERE 
    o.status = 'completed'
GROUP BY 
    f.food_id, f.food_name, f.category_id, c.category_name
ORDER BY 
    total_ordered DESC;

-- Create triggers for automatic calculations

-- Update order total after adding order details
DELIMITER //
CREATE TRIGGER after_order_detail_insert
AFTER INSERT ON order_details
FOR EACH ROW
BEGIN
    DECLARE order_total DECIMAL(10, 2);
    DECLARE discount_amt DECIMAL(10, 2);
    DECLARE final_amt DECIMAL(10, 2);
    
    -- Calculate the new total
    SELECT SUM(subtotal) INTO order_total FROM order_details WHERE order_id = NEW.order_id;
    
    -- Get current discount percentage
    SELECT discount_percent INTO @discount_percent FROM orders WHERE order_id = NEW.order_id;
    
    -- Calculate discount amount and final amount
    SET discount_amt = order_total * (@discount_percent / 100);
    SET final_amt = order_total - discount_amt;
    
    -- Update the order
    UPDATE orders SET 
        total_amount = order_total,
        discount_amount = discount_amt,
        final_amount = final_amt
    WHERE order_id = NEW.order_id;
END//
DELIMITER ;

-- Update order total after updating order details
DELIMITER //
CREATE TRIGGER after_order_detail_update
AFTER UPDATE ON order_details
FOR EACH ROW
BEGIN
    DECLARE order_total DECIMAL(10, 2);
    DECLARE discount_amt DECIMAL(10, 2);
    DECLARE final_amt DECIMAL(10, 2);
    
    -- Calculate the new total
    SELECT SUM(subtotal) INTO order_total FROM order_details WHERE order_id = NEW.order_id;
    
    -- Get current discount percentage
    SELECT discount_percent INTO @discount_percent FROM orders WHERE order_id = NEW.order_id;
    
    -- Calculate discount amount and final amount
    SET discount_amt = order_total * (@discount_percent / 100);
    SET final_amt = order_total - discount_amt;
    
    -- Update the order
    UPDATE orders SET 
        total_amount = order_total,
        discount_amount = discount_amt,
        final_amount = final_amt
    WHERE order_id = NEW.order_id;
END//
DELIMITER ;

-- Update order total after deleting order details
DELIMITER //
CREATE TRIGGER after_order_detail_delete
AFTER DELETE ON order_details
FOR EACH ROW
BEGIN
    DECLARE order_total DECIMAL(10, 2);
    DECLARE discount_amt DECIMAL(10, 2);
    DECLARE final_amt DECIMAL(10, 2);
    
    -- Calculate the new total
    SELECT SUM(subtotal) INTO order_total FROM order_details WHERE order_id = OLD.order_id;
    
    -- Handle NULL case when all details are deleted
    IF order_total IS NULL THEN
        SET order_total = 0;
    END IF;
    
    -- Get current discount percentage
    SELECT discount_percent INTO @discount_percent FROM orders WHERE order_id = OLD.order_id;
    
    -- Calculate discount amount and final amount
    SET discount_amt = order_total * (@discount_percent / 100);
    SET final_amt = order_total - discount_amt;
    
    -- Update the order
    UPDATE orders SET 
        total_amount = order_total,
        discount_amount = discount_amt,
        final_amount = final_amt
    WHERE order_id = OLD.order_id;
END//
DELIMITER ;

-- Update table status when order status changes
DELIMITER //
CREATE TRIGGER after_order_status_update
AFTER UPDATE ON orders
FOR EACH ROW
BEGIN
    -- When order is completed or cancelled, set table to available
    IF (NEW.status = 'completed' OR NEW.status = 'cancelled') AND OLD.status != 'completed' AND OLD.status != 'cancelled' THEN
        UPDATE tables SET status = 'available' WHERE table_id = NEW.table_id;
    -- When order is created or status changes to pending/processing, set table to occupied
    ELSEIF (NEW.status = 'pending' OR NEW.status = 'processing') AND 
           (OLD.status = 'completed' OR OLD.status = 'cancelled' OR OLD.status IS NULL) THEN
        UPDATE tables SET status = 'occupied' WHERE table_id = NEW.table_id;
    END IF;
END//
DELIMITER ;