<?php
/**
 * Staff Dashboard
 * 
 * Main dashboard for restaurant staff with table overview,
 * order management, and daily operations.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Table.php';
require_once '../../models/Order.php';
require_once '../../models/Payment.php';

// Check staff access
requireLogin();

// Initialize models
$tableModel = new Table();
$orderModel = new Order();
$paymentModel = new Payment();

// Get dashboard data
$tablesOverview = $tableModel->getTablesOverview();
$tableStats = $tableModel->getTableStats();
$dailyRevenue = $paymentModel->getDailyRevenue();
$activeOrders = $orderModel->getActiveOrders();

$pageTitle = __('dashboard') . ' ' . __('staff') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .table-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            transition: transform 0.3s ease;
        }
        .table-card:hover {
            transform: translateY(-5px);
        }
        .table-available {
            border-left: 5px solid #28a745;
        }
        .table-occupied {
            border-left: 5px solid #dc3545;
        }
        .table-reserved {
            border-left: 5px solid #ffc107;
        }
        .table-maintenance {
            border-left: 5px solid #6c757d;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .quick-action-btn {
            border-radius: 25px;
            padding: 12px 25px;
            font-weight: 500;
            margin: 0.25rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-utensils me-2"></i><?php echo APP_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tables.php">
                            <i class="fas fa-chair me-1"></i>Tables
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-receipt me-1"></i>Orders
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-1"></i>Profile
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-tachometer-alt me-2"></i>Staff Dashboard</h2>
            <div class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                <?php echo date('l, F j, Y'); ?>
            </div>
        </div>
        
        <?php displayFlashMessage(); ?>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo formatCurrency($dailyRevenue['total_revenue']); ?></h3>
                            <p class="mb-0">Today's Revenue</p>
                            <small><?php echo $dailyRevenue['total_orders']; ?> orders</small>
                        </div>
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo $tableStats['available_tables']; ?></h3>
                            <p class="mb-0">Available Tables</p>
                            <small>Out of <?php echo $tableStats['total_tables']; ?> total</small>
                        </div>
                        <i class="fas fa-chair fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo count($activeOrders); ?></h3>
                            <p class="mb-0">Active Orders</p>
                            <small>Pending & Processing</small>
                        </div>
                        <i class="fas fa-receipt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo $tableStats['occupied_tables']; ?></h3>
                            <p class="mb-0">Occupied Tables</p>
                            <small>Currently serving</small>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body text-center">
                        <a href="new-order.php" class="btn btn-primary quick-action-btn">
                            <i class="fas fa-plus me-2"></i>New Order
                        </a>
                        <a href="tables.php" class="btn btn-success quick-action-btn">
                            <i class="fas fa-chair me-2"></i>View Tables
                        </a>
                        <a href="orders.php" class="btn btn-info quick-action-btn">
                            <i class="fas fa-receipt me-2"></i>Manage Orders
                        </a>
                        <a href="payment.php" class="btn btn-warning quick-action-btn">
                            <i class="fas fa-credit-card me-2"></i>Process Payment
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tables Overview -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-chair me-2"></i>Tables Overview</h5>
                        <div>
                            <span class="badge bg-success me-1">Available</span>
                            <span class="badge bg-danger me-1">Occupied</span>
                            <span class="badge bg-warning me-1">Reserved</span>
                            <span class="badge bg-secondary">Maintenance</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($tablesOverview as $table): ?>
                                <div class="col-md-4 col-lg-3">
                                    <div class="card table-card table-<?php echo $table['status']; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-chair me-1"></i>
                                                    Table <?php echo htmlspecialchars($table['table_number']); ?>
                                                </h6>
                                                <span class="badge bg-<?php 
                                                    echo $table['status'] === 'available' ? 'success' : 
                                                        ($table['status'] === 'occupied' ? 'danger' : 
                                                        ($table['status'] === 'reserved' ? 'warning' : 'secondary')); 
                                                ?>">
                                                    <?php echo ucfirst($table['status']); ?>
                                                </span>
                                            </div>
                                            
                                            <p class="card-text small text-muted mb-2">
                                                <i class="fas fa-users me-1"></i>
                                                Capacity: <?php echo $table['capacity']; ?> people
                                            </p>
                                            
                                            <?php if ($table['order_id']): ?>
                                                <div class="border-top pt-2">
                                                    <small class="text-muted">
                                                        Order #<?php echo $table['order_id']; ?><br>
                                                        Amount: <?php echo formatCurrency($table['final_amount']); ?><br>
                                                        Staff: <?php echo htmlspecialchars($table['staff_name']); ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="mt-2">
                                                <?php if ($table['status'] === 'available'): ?>
                                                    <a href="new-order.php?table_id=<?php echo $table['table_id']; ?>" 
                                                       class="btn btn-sm btn-primary">
                                                        <i class="fas fa-plus me-1"></i>New Order
                                                    </a>
                                                <?php elseif ($table['status'] === 'occupied' && $table['order_id']): ?>
                                                    <a href="order-details.php?id=<?php echo $table['order_id']; ?>" 
                                                       class="btn btn-sm btn-info me-1">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </a>
                                                    <a href="payment.php?order_id=<?php echo $table['order_id']; ?>" 
                                                       class="btn btn-sm btn-success">
                                                        <i class="fas fa-credit-card me-1"></i>Pay
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    
    <script>
        // Auto-refresh dashboard every 30 seconds
        setInterval(function() {
            if (document.visibilityState === 'visible') {
                location.reload();
            }
        }, 30000);
        
        // Initialize tooltips and other components
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new mdb.Tooltip(tooltip);
            });
            
            // Add click handlers for table cards
            const tableCards = document.querySelectorAll('.table-card');
            tableCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    // Don't trigger if clicking on buttons
                    if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON') {
                        return;
                    }
                    
                    // Add visual feedback
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
