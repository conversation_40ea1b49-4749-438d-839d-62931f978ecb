<?php
/**
 * Debug Authentication Script
 * 
 * This script helps debug authentication issues by testing database connection,
 * user retrieval, and password verification.
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>Restaurant Management System - Authentication Debug</h2>";

// Test 1: Database Connection
echo "<h3>1. Database Connection Test</h3>";
if ($pdo) {
    echo "✅ Database connection successful<br>";
    echo "Database: " . $database->connect()->query("SELECT DATABASE()")->fetchColumn() . "<br>";
} else {
    echo "❌ Database connection failed: " . $database->getError() . "<br>";
    exit;
}

// Test 2: Check if users table exists and has data
echo "<h3>2. Users Table Test</h3>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    echo "✅ Users table exists with $userCount users<br>";
    
    // Show all users
    $stmt = $pdo->query("SELECT user_id, username, email, full_name, role_id FROM users");
    $users = $stmt->fetchAll();
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Role ID</th></tr>";
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>{$user['user_id']}</td>";
        echo "<td>{$user['username']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['full_name']}</td>";
        echo "<td>{$user['role_id']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (PDOException $e) {
    echo "❌ Error accessing users table: " . $e->getMessage() . "<br>";
}

// Test 3: Check roles table
echo "<h3>3. Roles Table Test</h3>";
try {
    $stmt = $pdo->query("SELECT * FROM roles");
    $roles = $stmt->fetchAll();
    echo "✅ Roles table exists with " . count($roles) . " roles<br>";
    foreach ($roles as $role) {
        echo "Role ID {$role['role_id']}: {$role['role_name']}<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error accessing roles table: " . $e->getMessage() . "<br>";
}

// Test 4: Test password hashing and verification
echo "<h3>4. Password Hash Test</h3>";
$testPassword = 'admin123';
$newHash = password_hash($testPassword, PASSWORD_DEFAULT);
echo "Test password: $testPassword<br>";
echo "New hash: $newHash<br>";
echo "Verification test: " . (password_verify($testPassword, $newHash) ? "✅ PASS" : "❌ FAIL") . "<br>";

// Test 5: Check existing password hashes
echo "<h3>5. Existing Password Hash Test</h3>";
try {
    $stmt = $pdo->query("SELECT username, password FROM users WHERE username IN ('admin', 'staff')");
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        echo "<strong>User: {$user['username']}</strong><br>";
        echo "Stored hash: {$user['password']}<br>";
        
        $testPass = $user['username'] === 'admin' ? 'admin123' : 'staff123';
        echo "Testing password: $testPass<br>";
        
        $isValid = password_verify($testPass, $user['password']);
        echo "Verification result: " . ($isValid ? "✅ VALID" : "❌ INVALID") . "<br>";
        
        if (!$isValid) {
            echo "🔧 Generating new hash for $testPass: " . password_hash($testPass, PASSWORD_DEFAULT) . "<br>";
        }
        echo "<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error testing passwords: " . $e->getMessage() . "<br>";
}

// Test 6: Test the User model authentication
echo "<h3>6. User Model Authentication Test</h3>";
require_once 'models/User.php';

$userModel = new User();

// Test admin login
echo "<strong>Testing admin login:</strong><br>";
$adminUser = $userModel->authenticate('admin', 'admin123');
if ($adminUser) {
    echo "✅ Admin authentication successful<br>";
    echo "User data: " . print_r($adminUser, true) . "<br>";
} else {
    echo "❌ Admin authentication failed<br>";
}

// Test staff login
echo "<strong>Testing staff login:</strong><br>";
$staffUser = $userModel->authenticate('staff', 'staff123');
if ($staffUser) {
    echo "✅ Staff authentication successful<br>";
    echo "User data: " . print_r($staffUser, true) . "<br>";
} else {
    echo "❌ Staff authentication failed<br>";
}

// Test 7: Fix password hashes if needed
echo "<h3>7. Password Hash Fix</h3>";
echo "<p>If the authentication tests above failed, click the button below to update the password hashes:</p>";
echo "<form method='POST'>";
echo "<input type='hidden' name='action' value='fix_passwords'>";
echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Fix Password Hashes</button>";
echo "</form>";

// Handle password fix
if ($_POST['action'] ?? '' === 'fix_passwords') {
    echo "<h4>Fixing Password Hashes...</h4>";
    
    try {
        // Update admin password
        $adminHash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = 'admin'");
        $stmt->execute([$adminHash]);
        echo "✅ Admin password hash updated<br>";
        
        // Update staff password
        $staffHash = password_hash('staff123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = 'staff'");
        $stmt->execute([$staffHash]);
        echo "✅ Staff password hash updated<br>";
        
        echo "<p style='color: green; font-weight: bold;'>Password hashes have been fixed! You can now try logging in again.</p>";
        echo "<a href='views/auth/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a>";
        
    } catch (PDOException $e) {
        echo "❌ Error fixing passwords: " . $e->getMessage() . "<br>";
    }
}

echo "<hr>";
echo "<p><strong>Debug completed.</strong> If all tests pass, the login should work correctly.</p>";
echo "<p><a href='views/auth/login.php'>Try Login Page</a> | <a href='index.php'>Go to Main Page</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #007bff; margin-top: 30px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
